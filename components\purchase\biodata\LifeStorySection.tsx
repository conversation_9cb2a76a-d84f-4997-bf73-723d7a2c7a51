import { useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@radix-ui/react-label";
import { Button } from "@/components/ui/button";
import { Sparkles } from "lucide-react";
import AIImprovePopup from "@/components/ui/ai-improve-popup";
import { geminiService } from "@/lib/services/geminiService";
import { useToast } from "@/hooks/useToast";

interface LifeStorySectionProps {
  description: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  // Data untuk AI context
  name?: string;
  birthPlace?: string;
  birthDate?: string;
  deathDate?: string;
  submittedBy?: string;
}

export default function LifeStorySection({
  description,
  onChange,
  name,
  birthPlace,
  birthDate,
  deathDate,
  submittedBy
}: LifeStorySectionProps) {
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [improvedText, setImprovedText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>();
  const { toast } = useToast();

  const handleImproveWithAI = async () => {
    if (!name?.trim()) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Nama almarhum/almarhumah harus diisi terlebih dahulu.",
      });
      return;
    }

    setIsPopupOpen(true);
    setIsLoading(true);
    setError(undefined);

    try {
      const result = await geminiService.improveLifeStory({
        name: name,
        birthPlace,
        birthDate,
        deathDate,
        currentStory: description,
        submittedBy
      });

      setImprovedText(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Terjadi kesalahan saat menghubungi AI';
      setError(errorMessage);
      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAcceptImprovedText = (text: string) => {
    // Create synthetic event to match onChange signature
    const syntheticEvent = {
      target: { value: text, name: 'description' }
    } as React.ChangeEvent<HTMLTextAreaElement>;

    onChange(syntheticEvent);
    toast({
      title: "Berhasil",
      description: "Kisah hidup telah diperbarui dengan bantuan AI.",
    });
  };

  const handleRegenerate = () => {
    handleImproveWithAI();
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-2">
        <Label htmlFor="description" className="text-sm sm:text-base text-memorial-200">
          Kisah Hidup
        </Label>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleImproveWithAI}
          className="bg-memorial-800/90 hover:bg-memorial-700 text-memorial-50 border border-memorial-700/50 h-9 md:h-10 px-3 md:px-4 text-xs md:text-sm transition-all duration-200"
          disabled={!name?.trim()}
        >
          <Sparkles className="h-3 w-3 mr-1" />
          Sempurnakan dengan AI
        </Button>
      </div>

      <Textarea
        id="description"
        name="description"
        value={description}
        onChange={onChange}
        className="bg-memorial-800 border-memorial-700 text-memorial-50 min-h-[120px] sm:min-h-[150px] text-sm sm:text-base"
        placeholder="Ceritakan kisah hidup almarhum/almarhumah..."
      />

      <AIImprovePopup
        isOpen={isPopupOpen}
        onClose={() => setIsPopupOpen(false)}
        onAccept={handleAcceptImprovedText}
        onRegenerate={handleRegenerate}
        improvedText={improvedText}
        isLoading={isLoading}
        error={error}
      />
    </div>
  );
}
