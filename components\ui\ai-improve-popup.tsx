import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { X, Check, RotateCcw, Sparkles } from "lucide-react";
import Loading from "@/components/ui/loading";

interface AIImprovePopupProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: (improvedText: string) => void;
  onRegenerate: () => void;
  improvedText: string;
  isLoading: boolean;
  error?: string;
}

export default function AIImprovePopup({
  isOpen,
  onClose,
  onAccept,
  onRegenerate,
  improvedText,
  isLoading,
  error
}: AIImprovePopupProps) {
  const [editedText, setEditedText] = useState(improvedText);

  // Update edited text when improved text changes
  useEffect(() => {
    setEditedText(improvedText);
  }, [improvedText]);

  if (!isOpen) return null;

  const handleAccept = () => {
    onAccept(editedText);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-hidden bg-memorial-900 border-memorial-700">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-lg font-semibold text-memorial-50 flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-candle-500" />
            Kisah Hidup yang Disempurnakan AI
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0 text-memorial-400 hover:text-memorial-50"
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {isLoading ? (
            <div className="py-8">
              <Loading message="AI sedang menyempurnakan kisah hidup..." />
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-400 mb-4">{error}</p>
              <Button
                onClick={onRegenerate}
                variant="outline"
                className="border-memorial-600 text-memorial-300 hover:bg-memorial-800"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Coba Lagi
              </Button>
            </div>
          ) : (
            <>
              <div>
                <label className="text-sm text-memorial-300 mb-2 block">
                  Hasil AI (Anda dapat mengedit sebelum memakai):
                </label>
                <Textarea
                  value={editedText}
                  onChange={(e) => setEditedText(e.target.value)}
                  className="bg-memorial-800 border-memorial-700 text-memorial-50 min-h-[200px] text-sm"
                  placeholder="Kisah hidup yang disempurnakan AI akan muncul di sini..."
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-3 pt-4">
                <Button
                  onClick={onRegenerate}
                  variant="outline"
                  className="border-memorial-600 text-memorial-300 hover:bg-memorial-800"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Generate Ulang
                </Button>
                
                <div className="flex gap-3 sm:ml-auto">
                  <Button
                    onClick={onClose}
                    variant="outline"
                    className="border-memorial-600 text-memorial-300 hover:bg-memorial-800"
                  >
                    Batal
                  </Button>
                  <Button
                    onClick={handleAccept}
                    className="bg-candle-500 hover:bg-candle-600 text-memorial-950"
                    disabled={!editedText.trim()}
                  >
                    <Check className="h-4 w-4 mr-2" />
                    Gunakan Teks Ini
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
