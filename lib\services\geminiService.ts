interface GeminiResponse {
  candidates: {
    content: {
      parts: {
        text: string;
      }[];
    };
  }[];
}

interface ImproveLifeStoryRequest {
  name: string;
  birthPlace?: string;
  birthDate?: string;
  deathDate?: string;
  currentStory?: string;
  submittedBy?: string;
}

export class GeminiService {
  private apiKey: string;
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_GOOGLE_GEMINI_API_KEY || '';
    if (!this.apiKey) {
      console.warn('Google Gemini API key not found');
    }
  }

  async improveLifeStory(data: ImproveLifeStoryRequest): Promise<string> {
    if (!this.apiKey) {
      throw new Error('Google Gemini API key not configured');
    }

    try {
      const prompt = this.buildLifeStoryPrompt(data);
      
      const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
          },
          safetySettings: [
            {
              category: "HARM_CATEGORY_HARASSMENT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_HATE_SPEECH", 
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_DANGEROUS_CONTENT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            }
          ]
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Gemini API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
      }

      const result: GeminiResponse = await response.json();
      
      if (!result.candidates || result.candidates.length === 0) {
        throw new Error('No response generated from Gemini API');
      }

      const generatedText = result.candidates[0].content.parts[0].text;
      return generatedText.trim();

    } catch (error) {
      console.error('Error calling Gemini API:', error);
      throw error;
    }
  }

  private buildLifeStoryPrompt(data: ImproveLifeStoryRequest): string {
    const { name, birthPlace, birthDate, deathDate, currentStory, submittedBy } = data;
    
    let prompt = `Tolong bantu saya menulis kisah hidup yang bermakna dan menyentuh hati untuk almarhum/almarhumah ${name}.

Informasi yang tersedia:`;

    if (birthPlace) prompt += `\n- Tempat lahir: ${birthPlace}`;
    if (birthDate) prompt += `\n- Tanggal lahir: ${birthDate}`;
    if (deathDate) prompt += `\n- Tanggal wafat: ${deathDate}`;
    if (submittedBy) prompt += `\n- Diajukan oleh: ${submittedBy}`;

    if (currentStory && currentStory.trim()) {
      prompt += `\n\nKisah hidup yang sudah ada:\n"${currentStory}"

Tolong perbaiki dan kembangkan kisah hidup di atas menjadi lebih bermakna, menyentuh, dan menghormati kenangan almarhum/almarhumah.`;
    } else {
      prompt += `\n\nTolong buatkan kisah hidup yang bermakna dan menyentuh hati berdasarkan informasi di atas.`;
    }

    prompt += `

Panduan penulisan:
- Gunakan bahasa Indonesia yang sopan dan penuh hormat
- Fokus pada aspek positif dan kenangan indah
- Buat narasi yang mengalir natural dan menyentuh hati
- Panjang sekitar 150-300 kata
- Hindari spekulasi berlebihan jika informasi terbatas
- Gunakan tone yang hangat namun menghormati

Mulai dengan kalimat pembuka yang menyentuh dan akhiri dengan penutup yang bermakna.`;

    return prompt;
  }
}

export const geminiService = new GeminiService();
