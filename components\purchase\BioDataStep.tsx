import { useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/errorHandler";
import { useBioDataForm } from "@/hooks/useBioDataForm";
import { useReligionData } from "@/hooks/useReligionData";
import { useImageUpload } from "@/hooks/useImageUpload";
import { useBioDataValidation } from "@/hooks/useBioDataValidation";
import BasicInfoSection from "./biodata/BasicInfoSection";
import ReligionSelector from "./biodata/ReligionSelector";
import ImageUploadSection from "./biodata/ImageUploadSection";
import EvidenceUploadSection from "./biodata/EvidenceUploadSection";
import LifeStorySection from "./biodata/LifeStorySection";
import FormActions from "./biodata/FormActions";

export interface BioData {
  name: string;
  birthPlace: string;
  birthDate: string;
  deathDate: string;
  religionId: string | null;  // UUID string or null
  submittedBy: string;
  description: string;  // Changed from lifeStory to match database schema
  images: File[];  // Back to File[] for upload
  evidenceName: string;
  evidenceImage: File | null;  // Back to File for upload
  locationId?: string;  // Fixed: Always string UUID, not number
}

export interface BioDataStepProps {
  initialData?: BioData;
  onProceedToLocation: (bioData: BioData) => void;
  onReset?: () => void;
  selectedPackage?: string; // Add package info to determine photo limit
}

export default function BioDataStep({ initialData, onProceedToLocation, onReset, selectedPackage }: BioDataStepProps) {
  // Determine max images based on package
  const getMaxImages = () => {
    if (selectedPackage === 'free') return 1;
    return 2; // standard and premium
  };

  const maxImages = getMaxImages();

  // Use custom hooks
  const { religions, religionOptions, loading: religionsLoading } = useReligionData();
  const { validateAndHandle } = useBioDataValidation();

  const {
    bioData,
    handleChange,
    handleSelectChange,
    updateBioData
  } = useBioDataForm({ initialData, religions });

  const {
    handleImageUpload,
    handleEvidenceImageUpload,
    handleRemoveImage,
    handleRemoveEvidenceImage,
    setImagesFromExternal,
    setEvidenceImageFromExternal
  } = useImageUpload({
    maxImages,
    onImagesChange: (images) => updateBioData({ images }),
    onEvidenceImageChange: (evidenceImage) => updateBioData({ evidenceImage })
  });




  // Sync images with bioData when they change
  useEffect(() => {
    setImagesFromExternal(bioData.images);
    setEvidenceImageFromExternal(bioData.evidenceImage);
  }, [bioData.images, bioData.evidenceImage, setImagesFromExternal, setEvidenceImageFromExternal]);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (validateAndHandle(bioData)) {
        onProceedToLocation(bioData);
      }
    } catch (error) {
      ErrorHandler.handle(error, "Submit Bio Data");
    }
  };
  
  return (
    <div className="bg-memorial-900 p-4 sm:p-6 rounded-lg border border-memorial-800">
      <h2 className="text-lg sm:text-xl font-semibold text-memorial-50 mb-4 sm:mb-6">Data Almarhum/Almarhumah</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
        {/* Basic Information */}
        <BasicInfoSection
          name={bioData.name}
          birthPlace={bioData.birthPlace}
          birthDate={bioData.birthDate}
          deathDate={bioData.deathDate}
          submittedBy={bioData.submittedBy}
          onChange={handleChange}
        />

        {/* Religion Selection */}
        <ReligionSelector
          religions={religions}
          selectedReligionId={bioData.religionId}
          onReligionChange={(value) => handleSelectChange('religionId', value)}
          loading={religionsLoading}
        />

        {/* Photo Upload */}
        <ImageUploadSection
          images={bioData.images}
          maxImages={maxImages}
          onImageUpload={handleImageUpload}
          onRemoveImage={handleRemoveImage}
        />

        {/* Evidence Upload */}
        <EvidenceUploadSection
          evidenceName={bioData.evidenceName}
          evidenceImage={bioData.evidenceImage}
          onEvidenceNameChange={handleChange}
          onEvidenceImageUpload={handleEvidenceImageUpload}
          onRemoveEvidenceImage={handleRemoveEvidenceImage}
        />

        {/* Life Story */}
        <LifeStorySection
          description={bioData.description}
          onChange={handleChange}
          name={bioData.name}
          birthPlace={bioData.birthPlace}
          birthDate={bioData.birthDate}
          deathDate={bioData.deathDate}
          submittedBy={bioData.submittedBy}
        />

        {/* Form Actions */}
        <FormActions
          onReset={onReset}
          onSubmit={handleSubmit}
          showResetButton={!!onReset}
        />
      </form>
    </div>
  );
}



